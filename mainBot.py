import argparse
import logging
import importlib
from config import Config
from core.base_trader import BaseTrader

# ───────────────────────────────── LOGGING ──────────────────────────────────
def setup_logging(log_level):
    """Setup logging configuration with proper level."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper(), logging.INFO),
        format="%(asctime)s  %(levelname)s  %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        force=True
    )
    return logging.getLogger("ZoneBoS")

def main():
    """Main entry point for the trading bot."""
    parser = argparse.ArgumentParser(description="Trading bot with breakout strategy.")
    
    # General arguments
    parser.add_argument("--backtest", action="store_true", help="Run backtest instead of live trading")
    parser.add_argument("--symbol", type=str, help="Trading symbol (e.g., XAUUSD)")
    parser.add_argument("--days", type=int, help="Number of days to backtest")
    parser.add_argument("--strategy", type=str, help="Trading strategy to use (default: breakout)")
    parser.add_argument("--timeframe", type=str, choices=["M1", "M5", "M15", "M30", "H1", "H4", "D1"],
                       help="Timeframe for data analysis (default: M1)")

    # Live trading arguments
    parser.add_argument("--tick-monitoring", action="store_true", help="Enable detailed tick monitoring")
    parser.add_argument("--tick-stats", action="store_true", help="Show tick statistics")

    # Backtest-specific arguments
    parser.add_argument("--save-results", action="store_true", help="Save backtest chart and trade history")

    args = parser.parse_args()

    # --- Config Loading Priority ---
    # 1. Base config
    cfg = Config()

    # 2. Strategy-specific config (overwrites base)
    strategy_name = args.strategy if args.strategy else cfg.STRATEGY
    try:
        strategy_module_path = f"strategies.{strategy_name}"
        strategy_module = importlib.import_module(strategy_module_path)

        # Try to get strategy instance factory function first (new class-based approach)
        if hasattr(strategy_module, 'get_strategy_instance'):
            # Create a temporary strategy instance to get configuration
            temp_strategy = strategy_module.get_strategy_instance(cfg)
            strategy_config = temp_strategy.get_strategy_config()
            for key, value in strategy_config.items():
                setattr(cfg, key, value)
        # Fall back to old function-based approach for backward compatibility
        elif hasattr(strategy_module, 'strategy_config'):
            for key, value in strategy_module.strategy_config.items():
                setattr(cfg, key, value)
        else:
            raise ImportError(f"Strategy module '{strategy_name}' must have either 'get_strategy_instance' function or 'strategy_config' dictionary")

    except ImportError as e:
        # A basic logger setup for config loading errors
        logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
        logging.error(f"Could not import strategy '{strategy_name}': {e}")
        return 1

    # 3. CLI arguments (overwrites strategy config)
    cfg.update_from_args(args)

    try:
        # Validate core configuration
        cfg.validate()

        # Validate strategy-specific configuration
        if hasattr(strategy_module, 'get_strategy_instance'):
            # New class-based approach
            temp_strategy = strategy_module.get_strategy_instance(cfg)
            strategy_config_dict = {key: getattr(cfg, key) for key in temp_strategy.get_strategy_config().keys()}
            temp_strategy.validate_config(strategy_config_dict)
        elif hasattr(strategy_module, 'validate_strategy_config'):
            # Old function-based approach
            strategy_config_dict = {key: getattr(cfg, key) for key in strategy_module.strategy_config.keys()}
            strategy_module.validate_strategy_config(strategy_config_dict)
    except ValueError as e:
        print(f"Configuration Error: {e}")
        return 1

    # Setup logging with config level
    log = setup_logging(cfg.LOG_LEVEL)

    try:
        # Create strategy instance
        if hasattr(strategy_module, 'get_strategy_instance'):
            # New class-based approach
            strategy_instance = strategy_module.get_strategy_instance(cfg)
            # Validate the strategy configuration for the actual instance
            strategy_config_dict = {key: getattr(cfg, key) for key in strategy_instance.get_strategy_config().keys()}
            strategy_instance.validate_config(strategy_config_dict)
            log.info(f"Loaded class-based strategy: {strategy_instance.__class__.__name__}")
        else:
            # Old function-based approach (backward compatibility)
            strategy_instance = strategy_module
            log.info(f"Loaded function-based strategy: {strategy_name}")

        # The new architecture uses a single BaseTrader engine
        log.info("Initializing trading engine...")
        trader = BaseTrader(cfg, strategy_instance)

        # The run method now handles both live and backtest modes
        trader.run(backtest=args.backtest)

        log.info("Trading bot session finished.")
        return 0
    except KeyboardInterrupt:
        log.info("Trading bot stopped by user.")
        return 0
    except ValueError as e:
        log.error(f"A validation or data error occurred: {e}", exc_info=True)
        return 1
    except ConnectionError as e:
        log.error(f"A network connection error occurred: {e}", exc_info=True)
        return 1
    except Exception as e:
        log.error(f"An unexpected fatal error occurred: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    main()
