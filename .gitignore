# TradingBot .gitignore
# Comprehensive ignore file for MetaTrader 5 Trading Bot

# ===== GENERATED FILES =====
# Backtest outputs and charts
backtest_chart.html
backtest_plots/
*.png
*.jpg
*.jpeg
*.gif

# Trade history and data files
trade_history_*.csv
zone_data.csv
*.log
logs/

# ===== PYTHON =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# ===== SENSITIVE DATA =====
# Configuration files with credentials (if any)
# Note: config.py is tracked but be careful with credentials
secrets.py
credentials.json
*.key
*.pem

# ===== DEVELOPMENT =====
# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# ===== OPERATING SYSTEM =====
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== METATRADER 5 SPECIFIC =====
# MT5 terminal files (if any get created)
*.ex5
*.mq5
terminal.exe
terminal64.exe

# ===== PROJECT SPECIFIC =====
# Temporary test files

temp_*
scratch_*

# Backup files
*.bak
*.backup
*.old

# Data dumps
*.dump
*.sql

# ===== ALWAYS INCLUDE =====
# Force include important files even if they match patterns above
!requirements.txt
!README.md
!LICENSE
!.gitignore
