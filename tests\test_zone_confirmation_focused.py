#!/usr/bin/env python3
"""
Focused test suite for zone confirmation functionality.
Tests the core zone confirmation logic without complex edge cases.
"""

import unittest
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config import Config
from strategies.breakout import BreakoutStrategy

class TestZoneConfirmationFocused(unittest.TestCase):
    """Focused test suite for zone confirmation system."""
    
    def setUp(self):
        """Set up test environment."""
        self.config = Config()
        self.strategy = BreakoutStrategy(self.config)
        
        # Merge strategy configuration
        strategy_config = self.strategy.get_strategy_config()
        for key, value in strategy_config.items():
            setattr(self.config, key, value)
        
        # Validate configuration
        self.strategy.validate_config(strategy_config)
        
        # Initialize with test data
        self.test_price = 2650.0  # Use realistic gold price
        self.config.ZONE_GAP = 2.5
        self.config.NUM_ZONES_PER_SIDE = 20
        self.strategy.initialize(self.test_price)
    
    def test_immediate_confirmation_works(self):
        """Test that immediate confirmation (1 tick) works correctly."""
        self.config.ZONE_CONFIRMATION_CANDLES = 1
        
        # Find a resistance zone above current price
        resistance_zone = None
        for zone in self.strategy.zones:
            if zone['zone_type'] == 'resistance' and zone['price'] > self.test_price:
                resistance_zone = zone
                break
        
        self.assertIsNotNone(resistance_zone, "No resistance zone found")
        zone_price = resistance_zone['price']
        
        # Test immediate signal generation
        signals = self.strategy.get_entry_signals(
            zone_price + 0.1, 
            self.test_price, 
            datetime.now()
        )
        
        self.assertEqual(len(signals), 1, "Should generate exactly 1 signal")
        signal_type, signal_data = signals[0]
        self.assertEqual(signal_type, "Buy", "Should be a Buy signal")
        self.assertEqual(signal_data['signal_type'], 'breakout', "Should be a breakout signal")
        self.assertEqual(signal_data['confirmations'], 1, "Should have 1 confirmation")
        
        # Zone should be deactivated after signal
        self.assertFalse(resistance_zone['is_active'], "Zone should be deactivated after signal")
    
    def test_multi_confirmation_works(self):
        """Test that multi-confirmation (2 ticks) works correctly."""
        self.config.ZONE_CONFIRMATION_CANDLES = 2
        
        # Find a resistance zone above current price
        resistance_zone = None
        for zone in self.strategy.zones:
            if zone['zone_type'] == 'resistance' and zone['price'] > self.test_price:
                resistance_zone = zone
                break
        
        self.assertIsNotNone(resistance_zone, "No resistance zone found")
        zone_price = resistance_zone['price']
        current_time = datetime.now()
        
        # First crossing - should NOT generate signal
        signals = self.strategy.get_entry_signals(
            zone_price + 0.1, 
            zone_price - 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 0, "Should not generate signal on first crossing")
        self.assertTrue(resistance_zone['is_active'], "Zone should remain active after first crossing")
        
        # Second crossing - should generate signal
        signals = self.strategy.get_entry_signals(
            zone_price + 0.1, 
            zone_price - 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 1, "Should generate signal on second crossing")
        signal_type, signal_data = signals[0]
        self.assertEqual(signal_type, "Buy", "Should be a Buy signal")
        self.assertEqual(signal_data['confirmations'], 2, "Should have 2 confirmations")
        
        # Zone should be deactivated after signal
        self.assertFalse(resistance_zone['is_active'], "Zone should be deactivated after signal")
    
    def test_triple_confirmation_works(self):
        """Test that triple confirmation (3 ticks) works correctly."""
        self.config.ZONE_CONFIRMATION_CANDLES = 3
        
        # Find a resistance zone above current price
        resistance_zone = None
        for zone in self.strategy.zones:
            if zone['zone_type'] == 'resistance' and zone['price'] > self.test_price:
                resistance_zone = zone
                break
        
        self.assertIsNotNone(resistance_zone, "No resistance zone found")
        zone_price = resistance_zone['price']
        current_time = datetime.now()
        
        # First crossing - should NOT generate signal
        signals = self.strategy.get_entry_signals(
            zone_price + 0.1, 
            zone_price - 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 0, "Should not generate signal on first crossing")
        self.assertTrue(resistance_zone['is_active'], "Zone should remain active")
        
        # Second crossing - should NOT generate signal
        signals = self.strategy.get_entry_signals(
            zone_price + 0.1, 
            zone_price - 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 0, "Should not generate signal on second crossing")
        self.assertTrue(resistance_zone['is_active'], "Zone should remain active")
        
        # Third crossing - should generate signal
        signals = self.strategy.get_entry_signals(
            zone_price + 0.1, 
            zone_price - 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 1, "Should generate signal on third crossing")
        signal_type, signal_data = signals[0]
        self.assertEqual(signal_type, "Buy", "Should be a Buy signal")
        self.assertEqual(signal_data['confirmations'], 3, "Should have 3 confirmations")
        
        # Zone should be deactivated after signal
        self.assertFalse(resistance_zone['is_active'], "Zone should be deactivated after signal")
    
    def test_support_zone_confirmation(self):
        """Test that support zone confirmation works correctly."""
        self.config.ZONE_CONFIRMATION_CANDLES = 2
        
        # Find a support zone below current price
        support_zone = None
        for zone in self.strategy.zones:
            if zone['zone_type'] == 'support' and zone['price'] < self.test_price:
                support_zone = zone
                break
        
        self.assertIsNotNone(support_zone, "No support zone found")
        zone_price = support_zone['price']
        current_time = datetime.now()
        
        # First crossing downward - should NOT generate signal
        signals = self.strategy.get_entry_signals(
            zone_price - 0.1, 
            zone_price + 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 0, "Should not generate signal on first crossing")
        self.assertTrue(support_zone['is_active'], "Zone should remain active")
        
        # Second crossing downward - should generate signal
        signals = self.strategy.get_entry_signals(
            zone_price - 0.1, 
            zone_price + 0.1, 
            current_time
        )
        self.assertEqual(len(signals), 1, "Should generate signal on second crossing")
        signal_type, signal_data = signals[0]
        self.assertEqual(signal_type, "Sell", "Should be a Sell signal")
        self.assertEqual(signal_data['confirmations'], 2, "Should have 2 confirmations")
        
        # Zone should be deactivated after signal
        self.assertFalse(support_zone['is_active'], "Zone should be deactivated after signal")
    
    def test_no_crossing_no_signal(self):
        """Test that no crossing means no signal."""
        self.config.ZONE_CONFIRMATION_CANDLES = 1
        
        # Find a resistance zone
        resistance_zone = None
        for zone in self.strategy.zones:
            if zone['zone_type'] == 'resistance' and zone['price'] > self.test_price:
                resistance_zone = zone
                break
        
        zone_price = resistance_zone['price']
        
        # Price movement that doesn't cross the zone
        signals = self.strategy.get_entry_signals(
            zone_price - 0.1, 
            zone_price - 0.2, 
            datetime.now()
        )
        self.assertEqual(len(signals), 0, "Should not generate signal without crossing")
        
        # Price exactly at zone level (no crossing)
        signals = self.strategy.get_entry_signals(
            zone_price, 
            zone_price, 
            datetime.now()
        )
        self.assertEqual(len(signals), 0, "Should not generate signal at exact zone price")
    
    def test_backward_compatibility(self):
        """Test backward compatibility functions."""
        # Test factory function
        from strategies.breakout import get_strategy_instance
        strategy_instance = get_strategy_instance(self.config)
        self.assertIsInstance(strategy_instance, BreakoutStrategy)
        
        # Test legacy config function
        from strategies.breakout import strategy_config
        self.assertIsInstance(strategy_config, dict)
        self.assertIn('ZONE_CONFIRMATION_CANDLES', strategy_config)
    
    def test_validation_prevents_invalid_input(self):
        """Test that input validation prevents invalid parameters."""
        # Test invalid tick prices
        signals = self.strategy.get_entry_signals(-100.0, 2650.0)
        self.assertEqual(signals, [], "Should return empty list for invalid tick price")
        
        signals = self.strategy.get_entry_signals(2650.0, None)
        self.assertEqual(signals, [], "Should return empty list for None prev_tick_price")
        
        signals = self.strategy.get_entry_signals(2650.0, -100.0)
        self.assertEqual(signals, [], "Should return empty list for invalid prev_tick_price")
    
    def test_configuration_validation(self):
        """Test configuration validation."""
        # Test valid configuration
        valid_config = {
            'ZONE_GAP': 2.5,
            'ZONE_CONFIRMATION_CANDLES': 3,
            'MAX_CONFIRMATION_ENTRIES': 1000,
            'MARTINGALE_MULT': 3.0,
            'MAX_MART_STEPS': 6,
            'SL_POINTS': 6.5,
            'TP_POINTS': 3.5,
            'NUM_ZONES_PER_SIDE': 100,
            'BASE_LOT_SIZE': 0.01,
            'MAX_SAFE_LOT_SIZE': 5.0,
            'ZONE_REFRESH_INTERVAL': 60
        }
        
        # Should not raise any exceptions
        self.strategy.validate_config(valid_config)
        
        # Test invalid configuration
        invalid_config = valid_config.copy()
        invalid_config['ZONE_CONFIRMATION_CANDLES'] = 0
        
        with self.assertRaises(ValueError):
            new_strategy = BreakoutStrategy(self.config)
            new_strategy.validate_config(invalid_config)
    
    def test_strategy_state_reporting(self):
        """Test strategy state reporting."""
        state = self.strategy.get_strategy_state()
        
        self.assertIsInstance(state, dict)
        self.assertIn('strategy_name', state)
        self.assertIn('zones_count', state)
        self.assertIn('active_zones', state)
        self.assertIn('validated_config', state)
        
        self.assertEqual(state['strategy_name'], 'BreakoutStrategy')
        self.assertGreater(state['zones_count'], 0)
        self.assertGreater(state['active_zones'], 0)
        self.assertTrue(state['validated_config'])


if __name__ == '__main__':
    # Run focused tests
    unittest.main(verbosity=2)