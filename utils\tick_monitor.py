#!/usr/bin/env python3
"""
Real-time tick monitoring utility for TradingBot.

This utility provides detailed tick monitoring and analysis capabilities
for live trading sessions.
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """Setup logging for tick monitor."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s  %(levelname)s  %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    return logging.getLogger("TickMonitor")

class TickMonitor:
    """Real-time tick monitoring and analysis."""
    
    def __init__(self, symbol="XAUUSD", update_interval=1):
        self.symbol = symbol
        self.update_interval = update_interval
        self.log = setup_logging()
        
        # Tick data storage
        self.ticks = []
        self.max_ticks = 10000
        
        # Statistics
        self.stats = {
            'total_ticks': 0,
            'start_time': None,
            'last_price': None,
            'price_range': {'min': float('inf'), 'max': float('-inf')},
            'spreads': [],
            'price_changes': [],
            'tick_intervals': []
        }
        
        # Initialize MT5 connection
        self.trader = None
        self._initialize_connection()
    
    def _initialize_connection(self):
        """Initialize MT5 connection."""
        try:
            from config import Config
            from strategies.bollinger import BollingerStrategy
            from core.base_trader import BaseTrader
            
            # Create minimal config for monitoring
            cfg = Config()
            cfg.ENABLE_TICK_MONITORING = True
            cfg.TICK_MONITORING_INTERVAL = self.update_interval
            cfg.TICK_STATS_INTERVAL = 10
            
            # Create strategy instance (needed for BaseTrader)
            strategy = BollingerStrategy(cfg)
            
            # Create trader instance
            self.trader = BaseTrader(cfg, strategy)
            
            # Test connection
            if not self.trader.broker_is_connected():
                if not self.trader.broker_initialize():
                    raise ConnectionError("Failed to connect to MT5")
            
            self.log.info(f"✅ Connected to MT5 for {self.symbol} monitoring")
            
        except Exception as e:
            self.log.error(f"❌ Failed to initialize MT5 connection: {e}")
            raise
    
    def get_current_tick(self) -> Optional[Dict]:
        """Get current tick data."""
        if not self.trader:
            return None
            
        tick_data = self.trader.broker_get_current_price(self.symbol)
        if not tick_data:
            return None
            
        return {
            'time': datetime.now(),
            'bid': tick_data['bid'],
            'ask': tick_data['ask'],
            'spread': tick_data['ask'] - tick_data['bid'],
            'mid_price': (tick_data['ask'] + tick_data['bid']) / 2
        }
    
    def update_statistics(self, tick: Dict):
        """Update tick statistics."""
        self.stats['total_ticks'] += 1
        
        if self.stats['start_time'] is None:
            self.stats['start_time'] = tick['time']
        
        # Update price range
        mid_price = tick['mid_price']
        self.stats['price_range']['min'] = min(self.stats['price_range']['min'], mid_price)
        self.stats['price_range']['max'] = max(self.stats['price_range']['max'], mid_price)
        
        # Track spreads
        self.stats['spreads'].append(tick['spread'])
        if len(self.stats['spreads']) > 1000:
            self.stats['spreads'] = self.stats['spreads'][-1000:]
        
        # Track price changes
        if self.stats['last_price'] is not None:
            price_change = mid_price - self.stats['last_price']
            self.stats['price_changes'].append(price_change)
            if len(self.stats['price_changes']) > 1000:
                self.stats['price_changes'] = self.stats['price_changes'][-1000:]
        
        self.stats['last_price'] = mid_price
        
        # Track tick intervals
        if len(self.ticks) > 0:
            interval = (tick['time'] - self.ticks[-1]['time']).total_seconds()
            self.stats['tick_intervals'].append(interval)
            if len(self.stats['tick_intervals']) > 1000:
                self.stats['tick_intervals'] = self.stats['tick_intervals'][-1000:]
    
    def get_statistics_summary(self) -> Dict:
        """Get comprehensive statistics summary."""
        if self.stats['total_ticks'] == 0:
            return {}
        
        # Calculate averages
        avg_spread = sum(self.stats['spreads']) / len(self.stats['spreads']) if self.stats['spreads'] else 0
        avg_price_change = sum(abs(pc) for pc in self.stats['price_changes']) / len(self.stats['price_changes']) if self.stats['price_changes'] else 0
        avg_tick_interval = sum(self.stats['tick_intervals']) / len(self.stats['tick_intervals']) if self.stats['tick_intervals'] else 0
        
        # Calculate session duration
        session_duration = (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0
        
        return {
            'total_ticks': self.stats['total_ticks'],
            'session_duration_minutes': session_duration / 60,
            'ticks_per_minute': self.stats['total_ticks'] / (session_duration / 60) if session_duration > 0 else 0,
            'current_price': self.stats['last_price'],
            'price_range': self.stats['price_range'],
            'price_range_points': self.stats['price_range']['max'] - self.stats['price_range']['min'],
            'average_spread': avg_spread,
            'average_price_change': avg_price_change,
            'average_tick_interval': avg_tick_interval,
            'tick_frequency_hz': 1.0 / avg_tick_interval if avg_tick_interval > 0 else 0
        }
    
    def print_statistics(self):
        """Print current statistics."""
        stats = self.get_statistics_summary()
        if not stats:
            self.log.info("No statistics available yet")
            return
        
        print("\n" + "="*60)
        print(f"📊 TICK MONITORING - {self.symbol}")
        print("="*60)
        print(f"Session Duration: {stats['session_duration_minutes']:.1f} minutes")
        print(f"Total Ticks: {stats['total_ticks']:,}")
        print(f"Ticks/Minute: {stats['ticks_per_minute']:.1f}")
        print(f"Tick Frequency: {stats['tick_frequency_hz']:.2f} Hz")
        print(f"Current Price: {stats['current_price']:.2f}")
        print(f"Price Range: {stats['price_range']['min']:.2f} - {stats['price_range']['max']:.2f} ({stats['price_range_points']:.2f} points)")
        print(f"Average Spread: {stats['average_spread']:.1f} points")
        print(f"Average Price Change: {stats['average_price_change']:.3f} points")
        print("="*60)
    
    def run_monitoring(self, duration_minutes=None):
        """Run tick monitoring for specified duration."""
        self.log.info(f"🚀 Starting tick monitoring for {self.symbol}")
        if duration_minutes:
            self.log.info(f"⏱️  Monitoring duration: {duration_minutes} minutes")
        else:
            self.log.info("⏱️  Monitoring indefinitely (Ctrl+C to stop)")
        
        start_time = datetime.now()
        last_stats_time = start_time
        
        try:
            while True:
                # Check duration limit
                if duration_minutes and (datetime.now() - start_time).total_seconds() > duration_minutes * 60:
                    self.log.info("⏰ Monitoring duration reached")
                    break
                
                # Get current tick
                tick = self.get_current_tick()
                if tick:
                    # Store tick
                    self.ticks.append(tick)
                    if len(self.ticks) > self.max_ticks:
                        self.ticks = self.ticks[-self.max_ticks:]
                    
                    # Update statistics
                    self.update_statistics(tick)
                    
                    # Print periodic statistics
                    if (datetime.now() - last_stats_time).total_seconds() >= 30:
                        self.print_statistics()
                        last_stats_time = datetime.now()
                
                time.sleep(self.update_interval)
                
        except KeyboardInterrupt:
            self.log.info("🛑 Monitoring stopped by user")
        
        # Final statistics
        self.print_statistics()
        self.log.info("✅ Tick monitoring completed")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Real-time tick monitoring utility")
    parser.add_argument("--symbol", type=str, default="XAUUSD", help="Trading symbol to monitor")
    parser.add_argument("--interval", type=float, default=1.0, help="Update interval in seconds")
    parser.add_argument("--duration", type=int, help="Monitoring duration in minutes")
    
    args = parser.parse_args()
    
    try:
        monitor = TickMonitor(symbol=args.symbol, update_interval=args.interval)
        monitor.run_monitoring(duration_minutes=args.duration)
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
