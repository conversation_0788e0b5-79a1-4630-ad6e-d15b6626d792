import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any, Optional
from .base_strategy import BaseStrategy

log = logging.getLogger("ZoneBoS.BollingerStrategy")

class BollingerStrategy(BaseStrategy):
    """Bollinger Bands trading strategy.
    
    This strategy uses Bollinger Bands to identify overbought/oversold conditions
    and potential trend reversals or continuations. It implements both mean reversion
    and trend following approaches based on how price interacts with the bands.
    
    Trading Logic:
    1. Mean Reversion: Buy when price touches lower band, sell when price touches upper band
    2. Trend Following: Buy on breakout above upper band, sell on breakdown below lower band
    3. Dynamic position sizing based on band width (volatility)
    4. Custom stop loss and take profit based on band positions
    """

    def __init__(self, config):
        super().__init__(config)
        
        # Bollinger Bands data
        self.bb_data = {
            'upper': [],
            'middle': [],  # SMA
            'lower': [],
            'width': [],   # Band width for volatility measurement
            'position': [] # Price position relative to bands
        }
        
        # Price history for calculations
        self.price_history = []
        self.last_signal_time = None
        self.current_trend = None  # 'bullish', 'bearish', or None
        
        # Strategy state
        self.initialized = False

    def get_strategy_config(self) -> Dict[str, Any]:
        """Return Bollinger Bands strategy configuration."""
        return {
            # Trade Settings
            "BASE_LOT_SIZE": 0.01,
            "SL_POINTS": 4.0,    # Stop-loss in price points
            "TP_POINTS": 8.0,    # Take-profit in price points (1:2 risk-reward)

            # Martingale Settings (disabled for this strategy)
            "ENABLE_MARTINGALE": False,
            "MARTINGALE_MULT": 1.0,
            "MAX_MART_STEPS": 1,
            "MAX_SAFE_LOT_SIZE": 1.0,

            # Bollinger Bands Settings
            "BB_PERIOD": 20,              # Period for moving average and standard deviation
            "BB_DEVIATION": 2.0,          # Standard deviation multiplier
            "BB_MIN_WIDTH": 0.5,          # Minimum band width to trade (avoid low volatility)
            "BB_MAX_WIDTH": 10.0,         # Maximum band width to trade (avoid extreme volatility)
            
            # Signal Settings
            "SIGNAL_MODE": "mean_reversion",  # "mean_reversion" or "trend_following"
            "MIN_SIGNAL_INTERVAL": 300,   # Minimum seconds between signals (5 minutes)
            "PRICE_TOUCH_THRESHOLD": 0.1, # How close price needs to be to band (in points)
            
            # Volatility-based position sizing
            "ENABLE_VOLATILITY_SIZING": True,
            "MIN_VOLATILITY_MULTIPLIER": 0.5,  # Minimum lot size multiplier
            "MAX_VOLATILITY_MULTIPLIER": 2.0,  # Maximum lot size multiplier
        }

    def validate_config(self, config_dict: Dict[str, Any]) -> None:
        """Validate Bollinger Bands strategy configuration."""
        if config_dict.get('BB_PERIOD', 1) < 2:
            raise ValueError("BB_PERIOD must be at least 2")
        if config_dict.get('BB_DEVIATION', 0) <= 0:
            raise ValueError("BB_DEVIATION must be greater than 0")
        if config_dict.get('BB_MIN_WIDTH', 0) < 0:
            raise ValueError("BB_MIN_WIDTH must be non-negative")
        if config_dict.get('BB_MAX_WIDTH', 1) <= config_dict.get('BB_MIN_WIDTH', 0):
            raise ValueError("BB_MAX_WIDTH must be greater than BB_MIN_WIDTH")
        if config_dict.get('MIN_SIGNAL_INTERVAL', 1) < 0:
            raise ValueError("MIN_SIGNAL_INTERVAL must be non-negative")
        if config_dict.get('SIGNAL_MODE') not in ['mean_reversion', 'trend_following']:
            raise ValueError("SIGNAL_MODE must be 'mean_reversion' or 'trend_following'")
        if config_dict.get('MIN_VOLATILITY_MULTIPLIER', 0) <= 0:
            raise ValueError("MIN_VOLATILITY_MULTIPLIER must be greater than 0")
        if config_dict.get('MAX_VOLATILITY_MULTIPLIER', 1) <= config_dict.get('MIN_VOLATILITY_MULTIPLIER', 0):
            raise ValueError("MAX_VOLATILITY_MULTIPLIER must be greater than MIN_VOLATILITY_MULTIPLIER")

        self.logger.info("Bollinger Bands strategy configuration validated successfully")

    def initialize(self, current_price: float, historical_data=None) -> None:
        """Initialize the Bollinger Bands strategy."""
        self.logger.info("Initializing Bollinger Bands strategy...")

        if historical_data is not None and not historical_data.empty:
            # Use historical data to calculate initial Bollinger Bands
            self._calculate_bollinger_bands(historical_data['close'].values)
            self.logger.info(f"Initialized with {len(historical_data)} historical data points")
        else:
            # Initialize with current price only
            self.price_history = [current_price]
            self._calculate_bollinger_bands(np.array([current_price]))
            self.logger.info("Initialized with current price only")

        self.initialized = True
        self.logger.info(f"Bollinger Bands strategy initialized successfully")
        self.logger.info(f"Period: {self.config.BB_PERIOD}, Deviation: {self.config.BB_DEVIATION}")
        self.logger.info(f"Signal Mode: {self.config.SIGNAL_MODE}")

    def update(self, current_price: float, current_time: Optional[datetime] = None) -> None:
        """Update strategy state with current market conditions."""
        if not self.initialized:
            self.logger.warning("Strategy not initialized, skipping update")
            return

        # Add current price to history
        self.price_history.append(current_price)
        
        # Keep only the required number of prices for calculations
        max_history = max(self.config.BB_PERIOD * 2, 100)  # Keep extra for stability
        if len(self.price_history) > max_history:
            self.price_history = self.price_history[-max_history:]

        # Recalculate Bollinger Bands
        self._calculate_bollinger_bands(np.array(self.price_history))
        
        # Update trend analysis
        self._update_trend_analysis(current_price)

    def _calculate_bollinger_bands(self, prices: np.ndarray) -> None:
        """Calculate Bollinger Bands for the given prices."""
        if len(prices) < self.config.BB_PERIOD:
            # Not enough data, use simple approximation
            current_price = prices[-1]
            sma = np.mean(prices)
            std = np.std(prices) if len(prices) > 1 else current_price * 0.01
            
            upper = sma + (self.config.BB_DEVIATION * std)
            lower = sma - (self.config.BB_DEVIATION * std)
            width = upper - lower
            position = (current_price - lower) / (upper - lower) if width > 0 else 0.5
            
            self.bb_data = {
                'upper': [upper],
                'middle': [sma],
                'lower': [lower],
                'width': [width],
                'position': [position]
            }
        else:
            # Calculate rolling Bollinger Bands
            period = self.config.BB_PERIOD
            deviation = self.config.BB_DEVIATION
            
            # Calculate for the last few periods to maintain history
            calculate_points = min(len(prices), 50)  # Calculate last 50 points
            start_idx = len(prices) - calculate_points
            
            upper_bands = []
            middle_bands = []
            lower_bands = []
            widths = []
            positions = []
            
            for i in range(start_idx, len(prices)):
                if i >= period - 1:
                    window = prices[i - period + 1:i + 1]
                    sma = np.mean(window)
                    std = np.std(window)
                    
                    upper = sma + (deviation * std)
                    lower = sma - (deviation * std)
                    width = upper - lower
                    
                    current_price = prices[i]
                    position = (current_price - lower) / width if width > 0 else 0.5
                    
                    upper_bands.append(upper)
                    middle_bands.append(sma)
                    lower_bands.append(lower)
                    widths.append(width)
                    positions.append(position)
            
            # Update the data (keep only recent values)
            max_keep = 20
            self.bb_data = {
                'upper': upper_bands[-max_keep:],
                'middle': middle_bands[-max_keep:],
                'lower': lower_bands[-max_keep:],
                'width': widths[-max_keep:],
                'position': positions[-max_keep:]
            }

    def _update_trend_analysis(self, current_price: float) -> None:
        """Update trend analysis based on price position relative to Bollinger Bands."""
        if not self.bb_data['position']:
            return
            
        current_position = self.bb_data['position'][-1]
        
        # Determine trend based on price position
        if current_position > 0.8:
            self.current_trend = 'bullish'
        elif current_position < 0.2:
            self.current_trend = 'bearish'
        else:
            self.current_trend = None  # Neutral/ranging

    def get_entry_signals(self, tick_price: float, prev_tick_price: Optional[float] = None,
                         current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate entry signals based on Bollinger Bands."""
        if not self.initialized or not self.bb_data['upper']:
            return []

        if prev_tick_price is None:
            return []

        if current_time is None:
            current_time = datetime.now()

        # Check minimum signal interval
        if (self.last_signal_time and 
            (current_time - self.last_signal_time).total_seconds() < self.config.MIN_SIGNAL_INTERVAL):
            return []

        signals = []
        
        # Get current Bollinger Bands values
        upper_band = self.bb_data['upper'][-1]
        lower_band = self.bb_data['lower'][-1]
        middle_band = self.bb_data['middle'][-1]
        band_width = self.bb_data['width'][-1]
        
        # Check if volatility is within acceptable range
        if (band_width < self.config.BB_MIN_WIDTH or 
            band_width > self.config.BB_MAX_WIDTH):
            return []

        threshold = self.config.PRICE_TOUCH_THRESHOLD
        
        if self.config.SIGNAL_MODE == "mean_reversion":
            # Mean reversion signals
            
            # Buy signal: Price touches or crosses below lower band
            if (prev_tick_price > lower_band + threshold and 
                tick_price <= lower_band + threshold):
                price_position = (tick_price - lower_band) / band_width if band_width > 0 else 0.5
                signal_data = {
                    "type": "bollinger_mean_reversion",
                    "direction": "buy",
                    "upper_band": upper_band,
                    "lower_band": lower_band,
                    "middle_band": middle_band,
                    "band_width": band_width,
                    "price_position": price_position
                }
                signals.append(("Buy", signal_data))
                
            # Sell signal: Price touches or crosses above upper band
            elif (prev_tick_price < upper_band - threshold and 
                  tick_price >= upper_band - threshold):
                price_position = (tick_price - lower_band) / band_width if band_width > 0 else 0.5
                signal_data = {
                    "type": "bollinger_mean_reversion",
                    "direction": "sell",
                    "upper_band": upper_band,
                    "lower_band": lower_band,
                    "middle_band": middle_band,
                    "band_width": band_width,
                    "price_position": price_position
                }
                signals.append(("Sell", signal_data))
                
        elif self.config.SIGNAL_MODE == "trend_following":
            # Trend following signals
            
            # Buy signal: Price breaks above upper band (bullish breakout)
            if (prev_tick_price <= upper_band and 
                tick_price > upper_band + threshold):
                price_position = (tick_price - lower_band) / band_width if band_width > 0 else 0.5
                signal_data = {
                    "type": "bollinger_trend_following",
                    "direction": "buy",
                    "upper_band": upper_band,
                    "lower_band": lower_band,
                    "middle_band": middle_band,
                    "band_width": band_width,
                    "price_position": price_position
                }
                signals.append(("Buy", signal_data))
                
            # Sell signal: Price breaks below lower band (bearish breakdown)
            elif (prev_tick_price >= lower_band and 
                  tick_price < lower_band - threshold):
                price_position = (tick_price - lower_band) / band_width if band_width > 0 else 0.5
                signal_data = {
                    "type": "bollinger_trend_following",
                    "direction": "sell",
                    "upper_band": upper_band,
                    "lower_band": lower_band,
                    "middle_band": middle_band,
                    "band_width": band_width,
                    "price_position": price_position
                }
                signals.append(("Sell", signal_data))

        if signals:
            self.last_signal_time = current_time
            for signal_type, signal_data in signals:
                self.logger.info(f"Bollinger Bands {self.config.SIGNAL_MODE} signal: {signal_type} at {tick_price:.2f}")
                self.logger.debug(f"Bands - Upper: {upper_band:.2f}, Middle: {middle_band:.2f}, Lower: {lower_band:.2f}, Width: {band_width:.2f}")

        return signals

    def get_strategy_state(self) -> Dict[str, Any]:
        """Get current strategy state for debugging/monitoring."""
        current_bb = {}
        if self.bb_data['upper']:
            current_bb = {
                "upper_band": self.bb_data['upper'][-1],
                "middle_band": self.bb_data['middle'][-1],
                "lower_band": self.bb_data['lower'][-1],
                "band_width": self.bb_data['width'][-1],
                "price_position": self.bb_data['position'][-1]
            }
        
        return {
            "strategy_name": self.__class__.__name__,
            "initialized": self.initialized,
            "signal_mode": getattr(self.config, 'SIGNAL_MODE', 'mean_reversion'),
            "bb_period": getattr(self.config, 'BB_PERIOD', 20),
            "bb_deviation": getattr(self.config, 'BB_DEVIATION', 2.0),
            "price_history_length": len(self.price_history),
            "current_trend": self.current_trend,
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "current_bollinger_bands": current_bb
        }
    
    def get_display_info(self, current_price: float) -> str:
        """Get bollinger bands strategy display information for live trading output."""
        if self.bb_data['upper']:
            upper = self.bb_data['upper'][-1]
            middle = self.bb_data['middle'][-1]
            lower = self.bb_data['lower'][-1]
            width = self.bb_data['width'][-1]
            position = self.bb_data['position'][-1]
            
            position_str = "Above Upper" if position > 0.8 else "Below Lower" if position < 0.2 else "Middle"
            
            return (f"📈 BOLLINGER BANDS | Price: {current_price:.2f} | "
                    f"Upper: {upper:.2f} | Middle: {middle:.2f} | Lower: {lower:.2f} | "
                    f"Width: {width:.2f} | Position: {position_str}")
        else:
            return f"📈 BOLLINGER BANDS | Price: {current_price:.2f} | Bands: Calculating..."
    
    def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
        """Get custom signal description for bollinger bands strategy."""
        signal_type = signal_data.get('signal_type', 'unknown')
        reason = signal_data.get('reason', 'unknown')
        
        if 'upper_band' in signal_type:
            return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: Upper Band {reason}"
        elif 'lower_band' in signal_type:
            return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: Lower Band {reason}"
        elif 'middle_band' in signal_type:
            return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: Middle Band {reason}"
        else:
            return f"📈 BOLLINGER SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"

    def get_position_size(self, signal_data: Dict[str, Any], trade_history: List[Dict]) -> float:
        """Calculate position size based on volatility (band width)."""
        base_lot_size = getattr(self.config, 'BASE_LOT_SIZE', 0.01)
        
        if not getattr(self.config, 'ENABLE_VOLATILITY_SIZING', True):
            return base_lot_size
            
        if not self.bb_data['width']:
            return base_lot_size
            
        # Get current band width (volatility measure)
        current_width = self.bb_data['width'][-1]
        
        # Calculate average width over recent periods for normalization
        recent_widths = self.bb_data['width'][-10:] if len(self.bb_data['width']) >= 10 else self.bb_data['width']
        avg_width = np.mean(recent_widths)
        
        if avg_width <= 0:
            return base_lot_size
            
        # Calculate volatility ratio (current vs average)
        volatility_ratio = current_width / avg_width
        
        # Inverse relationship: higher volatility = smaller position size
        volatility_multiplier = 1.0 / volatility_ratio
        
        # Apply limits
        min_mult = getattr(self.config, 'MIN_VOLATILITY_MULTIPLIER', 0.5)
        max_mult = getattr(self.config, 'MAX_VOLATILITY_MULTIPLIER', 2.0)
        volatility_multiplier = max(min_mult, min(max_mult, volatility_multiplier))
        
        final_lot_size = base_lot_size * volatility_multiplier
        
        self.logger.debug(f"Volatility-based position sizing: width={current_width:.2f}, avg_width={avg_width:.2f}, "
                         f"ratio={volatility_ratio:.2f}, multiplier={volatility_multiplier:.2f}, "
                         f"final_lot={final_lot_size:.3f}")
        
        return final_lot_size

    def get_stop_loss(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
        """Calculate stop loss based on Bollinger Bands."""
        # Default stop loss
        default_sl_points = getattr(self.config, 'SL_POINTS', 4.0)
        
        if signal_type == "Buy":
            default_sl = entry_price - default_sl_points
        else:  # Sell
            default_sl = entry_price + default_sl_points
            
        # Try to use Bollinger Bands for more intelligent stop loss
        if 'middle_band' in signal_data:
            middle_band = signal_data['middle_band']
            
            if self.config.SIGNAL_MODE == "mean_reversion":
                # For mean reversion, use middle band as stop loss
                if signal_type == "Buy":
                    bb_sl = middle_band - (default_sl_points * 0.5)  # Slightly below middle
                else:  # Sell
                    bb_sl = middle_band + (default_sl_points * 0.5)  # Slightly above middle
            else:  # trend_following
                # For trend following, use opposite band as stop loss
                if signal_type == "Buy":
                    bb_sl = signal_data.get('lower_band', entry_price) - default_sl_points
                else:  # Sell
                    bb_sl = signal_data.get('upper_band', entry_price) + default_sl_points
            
            # Use the more conservative (closer) stop loss
            if signal_type == "Buy":
                return max(default_sl, bb_sl)  # Higher value (closer to entry)
            else:  # Sell
                return min(default_sl, bb_sl)  # Lower value (closer to entry)
        
        return default_sl

    def get_take_profit(self, signal_type: str, entry_price: float, signal_data: Dict[str, Any]) -> float:
        """Calculate take profit based on Bollinger Bands."""
        # Default take profit
        default_tp_points = getattr(self.config, 'TP_POINTS', 8.0)
        
        if signal_type == "Buy":
            default_tp = entry_price + default_tp_points
        else:  # Sell
            default_tp = entry_price - default_tp_points
            
        # Try to use Bollinger Bands for more intelligent take profit
        if 'upper_band' in signal_data and 'lower_band' in signal_data:
            if self.config.SIGNAL_MODE == "mean_reversion":
                # For mean reversion, target opposite band
                if signal_type == "Buy":
                    bb_tp = signal_data['upper_band']
                else:  # Sell
                    bb_tp = signal_data['lower_band']
            else:  # trend_following
                # For trend following, use extended targets
                band_width = signal_data.get('band_width', default_tp_points)
                if signal_type == "Buy":
                    bb_tp = signal_data['upper_band'] + (band_width * 0.5)
                else:  # Sell
                    bb_tp = signal_data['lower_band'] - (band_width * 0.5)
            
            # Use the more aggressive (further) take profit, but limit it
            max_tp_points = default_tp_points * 2  # Don't go beyond 2x default
            
            if signal_type == "Buy":
                max_tp = entry_price + max_tp_points
                return min(default_tp, bb_tp, max_tp)  # Use most conservative
            else:  # Sell
                min_tp = entry_price - max_tp_points
                return max(default_tp, bb_tp, min_tp)  # Use most conservative
        
        return default_tp


# --- Factory Function for Strategy Loading ---

def get_strategy_instance(config):
    """Factory function to create a BollingerStrategy instance."""
    return BollingerStrategy(config)
