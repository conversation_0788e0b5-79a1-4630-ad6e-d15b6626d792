# Position-Aware Martingale System

## Overview

The position-aware martingale system ensures that each trading position maintains its own independent martingale sequence. When one position loses, only that position's next trade will have an increased lot size, while other positions continue with their default lot size.

## Problem Solved

**Previous Issue**: The martingale system was tracking losses globally across all positions, causing incorrect lot sizing in multi-position scenarios. When any position lost, all subsequent trades would use increased lot sizes.

**Solution**: Each position now tracks its own losing streak independently, ensuring proper risk management per position.

## How It Works

### 1. Position Isolation
Each position is identified by a unique `position_id` and maintains its own trade history:

```python
# Filter trades for specific position only
position_trades = [t for t in trade_history if t.get("position_id") == position_id]
closed_trades = [t for t in position_trades if t.get("status") in ["Win", "Loss"]]
```

### 2. Independent Losing Streak Tracking
Each position counts its own consecutive losses:

```python
consecutive_losses = 0
for trade in sorted(closed_trades, key=lambda t: t['close_time'], reverse=True):
    if trade['status'] == 'Loss':
        consecutive_losses += 1
    else:
        break  # Stop at first win
```

### 3. Position-Specific Lot Calculation
Lot size is calculated based on the position's own losing streak:

```python
if consecutive_losses == 0:
    next_lot = base_lot_size  # Reset after win
else:
    next_lot = base_lot_size * (martingale_mult ** consecutive_losses)
    next_lot = min(next_lot, max_safe_lot_size)  # Safety limit
```

## Example Scenario

Consider 3 positions with different trading histories:

### Position 1: Win → Loss → Loss → Win
- **History**: Win (0.01) → Loss (0.01) → Loss (0.03) → Win (0.09)
- **Current State**: Last trade was a WIN
- **Next Trade**: Uses base lot (0.01) - martingale reset

### Position 2: Loss → Loss → Loss
- **History**: Loss (0.01) → Loss (0.03) → Loss (0.09)
- **Current State**: 3 consecutive losses
- **Next Trade**: Uses increased lot (0.27) - continuing losing streak

### Position 3: Win
- **History**: Win (0.01)
- **Current State**: Last trade was a WIN
- **Next Trade**: Uses base lot (0.01) - martingale reset

## Configuration

The martingale system is configured in the strategy's `get_strategy_config()` method:

```python
{
    # Martingale Settings
    "ENABLE_MARTINGALE": True,
    "MARTINGALE_MULT": 3.0,        # Multiplier for each loss
    "MAX_MART_STEPS": 6,           # Maximum consecutive losses
    "MAX_SAFE_LOT_SIZE": 5.0,      # Safety limit for lot size
    
    # Multi-Position Settings
    "MAX_OPEN_POSITIONS": 3,       # Number of simultaneous positions
}
```

## Monitoring and Debugging

### Enhanced Logging
The system provides detailed logging for each position:

```
Position 1: Martingale state - Last trade was a win - martingale reset | Consecutive losses: 0 | Lot size: 0.01 (base: 0.01)
Position 2: Martingale state - Consecutive losses: 3 - lot size increased | Consecutive losses: 3 | Lot size: 0.27 (base: 0.01)
```

### Debug Method
Use `get_martingale_state_for_position(position_id)` to check any position's state:

```python
state = trader.get_martingale_state_for_position(1)
print(f"Position {state['position_id']}: {state['message']}")
print(f"Next lot size: {state['next_lot_size']:.2f}")
```

## Testing

Run the test script to verify position independence:

```bash
python test_martingale.py
```

This will demonstrate:
- Each position's independent martingale sequence
- Correct lot size calculations
- Position isolation verification

## Benefits

1. **Proper Risk Management**: Each position manages its own risk independently
2. **No Cross-Contamination**: Losses in one position don't affect others
3. **Flexible Position Sizing**: Different positions can be at different martingale stages
4. **Better Performance**: More accurate backtesting and live trading results
5. **Easier Debugging**: Clear logging shows each position's state

## Implementation Details

### Key Methods

- `_calculate_martingale_lot()`: Core calculation logic
- `get_martingale_state_for_position()`: Debug and monitoring
- `_get_martingale_step_from_lot()`: Integration with trading system

### Trade History Structure
Each trade includes position identification:

```python
{
    "position_id": 1,
    "status": "Loss",
    "close_time": datetime.now(),
    "lot": 0.03,
    "martingale_sequence_id": 123
}
```

### Safety Features

1. **Position ID Validation**: Ensures position_id is provided
2. **Safety Limits**: Prevents excessive lot sizes
3. **Error Handling**: Graceful fallback to base lot size
4. **Reset Logic**: Automatic reset after wins

## Usage in Live Trading

The system automatically works in both live trading and backtesting:

```bash
# Live trading with multiple positions
python mainBot.py --strategy breakout

# Backtesting with position-aware martingale
python mainBot.py --backtest --strategy breakout --days 10 --save-results
```

The logging will show how each position's martingale sequence evolves independently during trading. 